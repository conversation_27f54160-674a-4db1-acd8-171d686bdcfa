import { Directive, ElementRef, HostListener, Input, Renderer2, OnInit } from '@angular/core';

// Makes any element look nice when you hover over it
// Just add appHighlight to anything and it gets smooth animations and hover effects
// Works great for cards, buttons, or any clickable stuff
@Directive({
  selector: '[appHighlight]',
  standalone: true
})
export class HighlightDirective implements OnInit {
  // Change this to use a different hover color
  @Input() highlightColor = 'rgba(6, 182, 212, 0.12)';

  // What color it should be normally
  @Input() defaultColor = 'transparent';

  // How much bigger it gets on hover (1.03 = 3% bigger)
  @Input() scaleAmount = '1.03';

  // How fast the animation happens
  @Input() animationDuration = '0.3s';

  // How strong the shadow effect is
  @Input() shadowIntensity = 'medium';

  private originalBoxShadow: string = '';
  private isHovered = false;

  constructor(
    private el: ElementRef,
    private renderer: Renderer2
  ) {}

  ngOnInit() {
    // Remember what the shadow looked like originally
    this.originalBoxShadow = getComputedStyle(this.el.nativeElement).boxShadow;

    // Make the animations smooth
    this.renderer.setStyle(this.el.nativeElement, 'transition', `all ${this.animationDuration} cubic-bezier(0.4, 0, 0.2, 1)`);

    // Make sure keyboard users can focus on it too
    if (!this.el.nativeElement.getAttribute('tabindex')) {
      this.renderer.setAttribute(this.el.nativeElement, 'tabindex', '0');
    }
  }

  // When mouse enters the element
  @HostListener('mouseenter') onMouseEnter() {
    this.isHovered = true;
    this.applyHoverEffects();
  }

  // When mouse leaves the element
  @HostListener('mouseleave') onMouseLeave() {
    this.isHovered = false;
    this.removeHoverEffects();
  }

  // When someone tabs to this element
  @HostListener('focus') onFocus() {
    if (!this.isHovered) {
      this.applyFocusEffects();
    }
  }

  // When focus moves away
  @HostListener('blur') onBlur() {
    if (!this.isHovered) {
      this.removeHoverEffects();
    }
  }

  // Turn on all the cool hover effects
  private applyHoverEffects() {
    this.highlight(this.highlightColor);
    this.addScale();
    this.addShadow();
    this.addBorderGlow();
  }

  // Special effects for keyboard focus (a bit more subtle)
  private applyFocusEffects() {
    this.highlight('rgba(6, 182, 212, 0.08)');
    this.renderer.setStyle(this.el.nativeElement, 'outline', '2px solid rgba(6, 182, 212, 0.6)');
    this.renderer.setStyle(this.el.nativeElement, 'outline-offset', '2px');
  }

  // Turn everything back to normal
  private removeHoverEffects() {
    this.highlight(this.defaultColor);
    this.removeScale();
    this.removeShadow();
    this.removeBorderGlow();
    this.renderer.removeStyle(this.el.nativeElement, 'outline');
    this.renderer.removeStyle(this.el.nativeElement, 'outline-offset');
  }

  // Change the background color
  private highlight(color: string) {
    this.renderer.setStyle(this.el.nativeElement, 'background-color', color);
  }

  // Make it bigger and lift it up a bit
  private addScale() {
    this.renderer.setStyle(this.el.nativeElement, 'transform', `scale(${this.scaleAmount}) translateY(-2px)`);
  }

  // Put it back to normal size
  private removeScale() {
    this.renderer.setStyle(this.el.nativeElement, 'transform', 'scale(1) translateY(0)');
  }

  // Add a nice shadow effect
  private addShadow() {
    let shadowValue = '';

    switch (this.shadowIntensity) {
      case 'light':
        shadowValue = '0 4px 12px rgba(0, 0, 0, 0.1)';
        break;
      case 'medium':
        shadowValue = '0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(6, 182, 212, 0.15)';
        break;
      case 'heavy':
        shadowValue = '0 12px 35px rgba(0, 0, 0, 0.2), 0 6px 15px rgba(6, 182, 212, 0.2)';
        break;
      default:
        shadowValue = '0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(6, 182, 212, 0.15)';
    }

    this.renderer.setStyle(this.el.nativeElement, 'box-shadow', shadowValue);
  }

  // Put the shadow back to what it was
  private removeShadow() {
    this.renderer.setStyle(this.el.nativeElement, 'box-shadow', this.originalBoxShadow);
  }

  // Make the border glow a bit
  private addBorderGlow() {
    this.renderer.setStyle(this.el.nativeElement, 'border-color', 'rgba(6, 182, 212, 0.4)');
  }

  // Remove the border glow
  private removeBorderGlow() {
    this.renderer.removeStyle(this.el.nativeElement, 'border-color');
  }
}
