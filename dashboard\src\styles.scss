/* Main styles that work everywhere in the app */

:root {
  /* Main colors - purple/indigo theme */
  --primary-color: #6366f1;
  --primary-dark: #4f46e5;
  --primary-light: #8b5cf6;
  --primary-lighter: #a5b4fc;
  --primary-lightest: #e0e7ff;

  /* Secondary colors - green for success stuff */
  --secondary-color: #10b981;
  --secondary-dark: #059669;
  --secondary-light: #34d399;
  --secondary-lighter: #6ee7b7;
  --secondary-lightest: #d1fae5;

  /* Accent colors - cyan/teal for highlights */
  --accent-color: #06b6d4;
  --accent-dark: #0891b2;
  --accent-light: #22d3ee;
  --accent-lighter: #67e8f9;
  --accent-lightest: #cffafe;

  /* Colors for different states */
  --success-color: #10b981;
  --warning-color: #f97316;
  --error-color: #ef4444;
  --info-color: #06b6d4;

  /* Background and surface colors */
  --background-color: #f8fafc;
  --background-secondary: #f1f5f9;
  --surface-color: #ffffff;
  --surface-secondary: #f8fafc;
  --surface-elevated: #ffffff;

  /* Text colors from dark to light */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-muted: #94a3b8;
  --text-inverse: #ffffff;

  /* Border colors */
  --border-color: #e2e8f0;
  --border-secondary: #cbd5e1;
  --border-focus: var(--primary-color);
  --border-error: var(--error-color);

  /* Shadow System - Enhanced Depth */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* Legacy shadow names for backward compatibility */
  --shadow-light: var(--shadow-sm);
  --shadow-medium: var(--shadow-md);
  --shadow-large: var(--shadow-lg);

  /* Interactive States */
  --hover-overlay: rgba(0, 0, 0, 0.05);
  --focus-ring: 0 0 0 3px rgba(99, 102, 241, 0.1);
  --active-overlay: rgba(0, 0, 0, 0.1);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
  --gradient-background: linear-gradient(135deg, var(--background-color) 0%, var(--background-secondary) 100%);
  --gradient-surface: linear-gradient(135deg, var(--surface-color) 0%, var(--surface-secondary) 100%);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: var(--gradient-background);
  color: var(--text-primary);
  line-height: 1.6;
  min-height: 100vh;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Button styles */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  min-height: 44px; /* Accessibility - minimum touch target */
}

.btn:focus {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-dark) 0%, #3730a3 100%);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-secondary {
  background-color: var(--surface-color);
  color: var(--text-primary);
  border: 2px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--surface-secondary);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary:active {
  transform: translateY(0);
  background-color: var(--primary-lightest);
}

/* Success Button */
.btn-success {
  background: var(--gradient-secondary);
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.btn-success:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--secondary-dark) 0%, #047857 100%);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* Warning Button */
.btn-warning {
  background: linear-gradient(135deg, var(--warning-color) 0%, var(--accent-dark) 100%);
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.btn-warning:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--accent-dark) 0%, #b45309 100%);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* Error Button */
.btn-error {
  background: linear-gradient(135deg, var(--error-color) 0%, #dc2626 100%);
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.btn-error:hover:not(:disabled) {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* Small Button Variant */
.btn-sm {
  padding: 8px 16px;
  font-size: 12px;
  min-height: 36px;
}

/* Large Button Variant */
.btn-lg {
  padding: 16px 32px;
  font-size: 16px;
  min-height: 52px;
}

html, body { height: 100%; }
body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }
