import { Injectable, inject } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { UserService } from '../services/user.service';
import * as UserActions from './user.actions';

@Injectable()
export class UserEffects {
  private actions$ = inject(Actions);
  private userService = inject(UserService);

  loadUsers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(UserActions.loadUsers),
      switchMap(({ page, limit }) => {
        const skip = (page - 1) * limit;
        return this.userService.getUsers(limit, skip).pipe(
          map(response => UserActions.loadUsersSuccess({ response, page })),
          catchError(error => of(UserActions.loadUsersFailure({
            error: error.message || 'Failed to load users'
          })))
        );
      })
    )
  );

  loadUserDetail$ = createEffect(() =>
    this.actions$.pipe(
      ofType(UserActions.loadUserDetail),
      switchMap(({ userId }) =>
        this.userService.getUserById(userId).pipe(
          map(user => {
            if (user) {
              return UserActions.loadUserDetailSuccess({ user });
            } else {
              return UserActions.loadUserDetailFailure({
                error: 'User not found'
              });
            }
          }),
          catchError(error => of(UserActions.loadUserDetailFailure({
            error: error.message || 'Failed to load user details'
          })))
        )
      )
    )
  );
}
