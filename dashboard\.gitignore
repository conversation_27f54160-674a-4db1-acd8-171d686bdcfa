# Files to ignore when committing to git

# Build files
/dist
/tmp
/out-tsc
/bazel-out

# Node.js stuff
/node_modules
npm-debug.log
yarn-error.log

# Editor files
.idea/
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

# Random stuff
/.angular/cache
.sass-cache/
/connect.lock
/coverage
/libpeerconnection.log
testem.log
/typings

# OS files
.DS_Store
Thumbs.db
