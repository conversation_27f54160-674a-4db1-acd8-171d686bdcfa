import { Component, Output, EventEmitter, OnDestroy } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { UserService, User } from '../../services/user.service';
import { Subject, debounceTime, distinctUntilChanged, switchMap, takeUntil, of } from 'rxjs';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [FormsModule, CommonModule],
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss'
})
export class HeaderComponent implements OnDestroy {
  searchQuery: string = '';
  searchResults: User[] = [];
  showSearchResults: boolean = false;
  showSearchHelp: boolean = false;

  isSearching: boolean = false;

  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  @Output() searchUser = new EventEmitter<number>();

  constructor(
    private router: Router,
    private userService: UserService
  ) {
    // Set up the search to work as you type
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(query => {
        if (query.trim().length === 0) {
          this.isSearching = false;
          return of({ users: [], total: 0, skip: 0, limit: 0 });
        }

        this.isSearching = true;

        // If they typed a number, search by ID. Otherwise search by name
        const numericQuery = parseInt(query, 10);
        if (!isNaN(numericQuery) && numericQuery > 0) {
          return this.userService.getUserById(numericQuery).pipe(
            switchMap(user => {
              if (user) {
                return of({ users: [user], total: 1, skip: 0, limit: 1 });
              } else {
                return of({ users: [], total: 0, skip: 0, limit: 0 });
              }
            })
          );
        } else {
          return this.userService.searchUsers(query);
        }
      }),
      takeUntil(this.destroy$)
    ).subscribe(response => {
      this.isSearching = false;
      this.searchResults = response.users.slice(0, 5);
      this.showSearchResults = this.searchQuery.trim().length > 0;
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Simple methods
  onSearchInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchQuery = target.value;
    this.searchSubject.next(this.searchQuery);
  }

  onSearchFocus(): void {
    // Simple focus handler - no special logic needed
  }



  onSearchEnter(): void {
    if (!this.searchQuery || this.searchQuery.trim().length === 0) {
      return;
    }

    // If it's a number, go directly to that user
    const numericQuery = parseInt(this.searchQuery, 10);
    if (!isNaN(numericQuery) && numericQuery > 0) {
      this.router.navigate(['/user', numericQuery]);
      this.clearSearch();
      return;
    }

    // Otherwise, go to first search result
    if (this.searchResults.length > 0) {
      this.router.navigate(['/user', this.searchResults[0].id]);
      this.clearSearch();
    }
  }

  selectUser(userId: number): void {
    this.router.navigate(['/user', userId]);
    this.clearSearch();
  }

  clearSearch(): void {
    this.searchQuery = '';
    this.searchResults = [];
    this.showSearchResults = false;
    this.isSearching = false;
  }

  hideSearchHelp(): void {
    this.showSearchHelp = false;
  }

  // Makes the search term show up highlighted in results
  highlightText(text: string, searchTerm: string): string {
    if (!searchTerm || !text) {
      return text;
    }

    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<mark class="highlight">$1</mark>');
  }

}
