{"version": 3, "sources": ["../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/performanceTimestampProvider.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/animationFrameProvider.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/dom/animationFrames.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/util/Immediate.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/immediateProvider.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/AsapAction.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/AsapScheduler.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/asap.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/QueueAction.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/QueueScheduler.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/queue.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/AnimationFrameAction.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/AnimationFrameScheduler.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/animationFrame.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/VirtualTimeScheduler.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/util/isObservable.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/lastValueFrom.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/firstValueFrom.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/bindCallbackInternals.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/bindCallback.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/bindNodeCallback.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/defer.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/connectable.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/forkJoin.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/fromEvent.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/fromEventPattern.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/generate.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/iif.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/merge.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/never.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/pairs.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/partition.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/range.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/using.js"], "sourcesContent": ["export var performanceTimestampProvider = {\n  now: function () {\n    return (performanceTimestampProvider.delegate || performance).now();\n  },\n  delegate: undefined\n};\n", "import { __read, __spreadArray } from \"tslib\";\nimport { Subscription } from '../Subscription';\nexport var animationFrameProvider = {\n  schedule: function (callback) {\n    var request = requestAnimationFrame;\n    var cancel = cancelAnimationFrame;\n    var delegate = animationFrameProvider.delegate;\n    if (delegate) {\n      request = delegate.requestAnimationFrame;\n      cancel = delegate.cancelAnimationFrame;\n    }\n    var handle = request(function (timestamp) {\n      cancel = undefined;\n      callback(timestamp);\n    });\n    return new Subscription(function () {\n      return cancel === null || cancel === void 0 ? void 0 : cancel(handle);\n    });\n  },\n  requestAnimationFrame: function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    var delegate = animationFrameProvider.delegate;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.requestAnimationFrame) || requestAnimationFrame).apply(void 0, __spreadArray([], __read(args)));\n  },\n  cancelAnimationFrame: function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    var delegate = animationFrameProvider.delegate;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.cancelAnimationFrame) || cancelAnimationFrame).apply(void 0, __spreadArray([], __read(args)));\n  },\n  delegate: undefined\n};\n", "import { Observable } from '../../Observable';\nimport { performanceTimestampProvider } from '../../scheduler/performanceTimestampProvider';\nimport { animationFrameProvider } from '../../scheduler/animationFrameProvider';\nexport function animationFrames(timestampProvider) {\n  return timestampProvider ? animationFramesFactory(timestampProvider) : DEFAULT_ANIMATION_FRAMES;\n}\nfunction animationFramesFactory(timestampProvider) {\n  return new Observable(function (subscriber) {\n    var provider = timestampProvider || performanceTimestampProvider;\n    var start = provider.now();\n    var id = 0;\n    var run = function () {\n      if (!subscriber.closed) {\n        id = animationFrameProvider.requestAnimationFrame(function (timestamp) {\n          id = 0;\n          var now = provider.now();\n          subscriber.next({\n            timestamp: timestampProvider ? now : timestamp,\n            elapsed: now - start\n          });\n          run();\n        });\n      }\n    };\n    run();\n    return function () {\n      if (id) {\n        animationFrameProvider.cancelAnimationFrame(id);\n      }\n    };\n  });\n}\nvar DEFAULT_ANIMATION_FRAMES = animationFramesFactory();\n", "var nextHandle = 1;\nvar resolved;\nvar activeHandles = {};\nfunction findAndClearHandle(handle) {\n  if (handle in activeHandles) {\n    delete activeHandles[handle];\n    return true;\n  }\n  return false;\n}\nexport var Immediate = {\n  setImmediate: function (cb) {\n    var handle = nextHandle++;\n    activeHandles[handle] = true;\n    if (!resolved) {\n      resolved = Promise.resolve();\n    }\n    resolved.then(function () {\n      return findAndClearHandle(handle) && cb();\n    });\n    return handle;\n  },\n  clearImmediate: function (handle) {\n    findAndClearHandle(handle);\n  }\n};\nexport var TestTools = {\n  pending: function () {\n    return Object.keys(activeHandles).length;\n  }\n};\n", "import { __read, __spreadArray } from \"tslib\";\nimport { Immediate } from '../util/Immediate';\nvar setImmediate = Immediate.setImmediate,\n  clearImmediate = Immediate.clearImmediate;\nexport var immediateProvider = {\n  setImmediate: function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    var delegate = immediateProvider.delegate;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.setImmediate) || setImmediate).apply(void 0, __spreadArray([], __read(args)));\n  },\n  clearImmediate: function (handle) {\n    var delegate = immediateProvider.delegate;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearImmediate) || clearImmediate)(handle);\n  },\n  delegate: undefined\n};\n", "import { __extends } from \"tslib\";\nimport { AsyncAction } from './AsyncAction';\nimport { immediateProvider } from './immediateProvider';\nvar AsapAction = function (_super) {\n  __extends(AsapAction, _super);\n  function AsapAction(scheduler, work) {\n    var _this = _super.call(this, scheduler, work) || this;\n    _this.scheduler = scheduler;\n    _this.work = work;\n    return _this;\n  }\n  AsapAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (delay !== null && delay > 0) {\n      return _super.prototype.requestAsyncId.call(this, scheduler, id, delay);\n    }\n    scheduler.actions.push(this);\n    return scheduler._scheduled || (scheduler._scheduled = immediateProvider.setImmediate(scheduler.flush.bind(scheduler, undefined)));\n  };\n  AsapAction.prototype.recycleAsyncId = function (scheduler, id, delay) {\n    var _a;\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (delay != null ? delay > 0 : this.delay > 0) {\n      return _super.prototype.recycleAsyncId.call(this, scheduler, id, delay);\n    }\n    var actions = scheduler.actions;\n    if (id != null && ((_a = actions[actions.length - 1]) === null || _a === void 0 ? void 0 : _a.id) !== id) {\n      immediateProvider.clearImmediate(id);\n      if (scheduler._scheduled === id) {\n        scheduler._scheduled = undefined;\n      }\n    }\n    return undefined;\n  };\n  return AsapAction;\n}(AsyncAction);\nexport { AsapAction };\n", "import { __extends } from \"tslib\";\nimport { AsyncScheduler } from './AsyncScheduler';\nvar AsapScheduler = function (_super) {\n  __extends(AsapScheduler, _super);\n  function AsapScheduler() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  AsapScheduler.prototype.flush = function (action) {\n    this._active = true;\n    var flushId = this._scheduled;\n    this._scheduled = undefined;\n    var actions = this.actions;\n    var error;\n    action = action || actions.shift();\n    do {\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    } while ((action = actions[0]) && action.id === flushId && actions.shift());\n    this._active = false;\n    if (error) {\n      while ((action = actions[0]) && action.id === flushId && actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  };\n  return AsapScheduler;\n}(AsyncScheduler);\nexport { AsapScheduler };\n", "import { AsapAction } from './AsapAction';\nimport { AsapScheduler } from './AsapScheduler';\nexport var asapScheduler = new AsapScheduler(AsapAction);\nexport var asap = asapScheduler;\n", "import { __extends } from \"tslib\";\nimport { AsyncAction } from './AsyncAction';\nvar QueueAction = function (_super) {\n  __extends(QueueAction, _super);\n  function QueueAction(scheduler, work) {\n    var _this = _super.call(this, scheduler, work) || this;\n    _this.scheduler = scheduler;\n    _this.work = work;\n    return _this;\n  }\n  QueueAction.prototype.schedule = function (state, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (delay > 0) {\n      return _super.prototype.schedule.call(this, state, delay);\n    }\n    this.delay = delay;\n    this.state = state;\n    this.scheduler.flush(this);\n    return this;\n  };\n  QueueAction.prototype.execute = function (state, delay) {\n    return delay > 0 || this.closed ? _super.prototype.execute.call(this, state, delay) : this._execute(state, delay);\n  };\n  QueueAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (delay != null && delay > 0 || delay == null && this.delay > 0) {\n      return _super.prototype.requestAsyncId.call(this, scheduler, id, delay);\n    }\n    scheduler.flush(this);\n    return 0;\n  };\n  return QueueAction;\n}(AsyncAction);\nexport { QueueAction };\n", "import { __extends } from \"tslib\";\nimport { AsyncScheduler } from './AsyncScheduler';\nvar QueueScheduler = function (_super) {\n  __extends(QueueScheduler, _super);\n  function QueueScheduler() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  return QueueScheduler;\n}(AsyncScheduler);\nexport { QueueScheduler };\n", "import { QueueAction } from './QueueAction';\nimport { QueueScheduler } from './QueueScheduler';\nexport var queueScheduler = new QueueScheduler(QueueAction);\nexport var queue = queueScheduler;\n", "import { __extends } from \"tslib\";\nimport { AsyncAction } from './AsyncAction';\nimport { animationFrameProvider } from './animationFrameProvider';\nvar AnimationFrameAction = function (_super) {\n  __extends(AnimationFrameAction, _super);\n  function AnimationFrameAction(scheduler, work) {\n    var _this = _super.call(this, scheduler, work) || this;\n    _this.scheduler = scheduler;\n    _this.work = work;\n    return _this;\n  }\n  AnimationFrameAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (delay !== null && delay > 0) {\n      return _super.prototype.requestAsyncId.call(this, scheduler, id, delay);\n    }\n    scheduler.actions.push(this);\n    return scheduler._scheduled || (scheduler._scheduled = animationFrameProvider.requestAnimationFrame(function () {\n      return scheduler.flush(undefined);\n    }));\n  };\n  AnimationFrameAction.prototype.recycleAsyncId = function (scheduler, id, delay) {\n    var _a;\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (delay != null ? delay > 0 : this.delay > 0) {\n      return _super.prototype.recycleAsyncId.call(this, scheduler, id, delay);\n    }\n    var actions = scheduler.actions;\n    if (id != null && id === scheduler._scheduled && ((_a = actions[actions.length - 1]) === null || _a === void 0 ? void 0 : _a.id) !== id) {\n      animationFrameProvider.cancelAnimationFrame(id);\n      scheduler._scheduled = undefined;\n    }\n    return undefined;\n  };\n  return AnimationFrameAction;\n}(AsyncAction);\nexport { AnimationFrameAction };\n", "import { __extends } from \"tslib\";\nimport { AsyncScheduler } from './AsyncScheduler';\nvar AnimationFrameScheduler = function (_super) {\n  __extends(AnimationFrameScheduler, _super);\n  function AnimationFrameScheduler() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  AnimationFrameScheduler.prototype.flush = function (action) {\n    this._active = true;\n    var flushId;\n    if (action) {\n      flushId = action.id;\n    } else {\n      flushId = this._scheduled;\n      this._scheduled = undefined;\n    }\n    var actions = this.actions;\n    var error;\n    action = action || actions.shift();\n    do {\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    } while ((action = actions[0]) && action.id === flushId && actions.shift());\n    this._active = false;\n    if (error) {\n      while ((action = actions[0]) && action.id === flushId && actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  };\n  return AnimationFrameScheduler;\n}(AsyncScheduler);\nexport { AnimationFrameScheduler };\n", "import { AnimationFrameAction } from './AnimationFrameAction';\nimport { AnimationFrameScheduler } from './AnimationFrameScheduler';\nexport var animationFrameScheduler = new AnimationFrameScheduler(AnimationFrameAction);\nexport var animationFrame = animationFrameScheduler;\n", "import { __extends } from \"tslib\";\nimport { AsyncAction } from './AsyncAction';\nimport { Subscription } from '../Subscription';\nimport { AsyncScheduler } from './AsyncScheduler';\nvar VirtualTimeScheduler = function (_super) {\n  __extends(VirtualTimeScheduler, _super);\n  function VirtualTimeScheduler(schedulerActionCtor, maxFrames) {\n    if (schedulerActionCtor === void 0) {\n      schedulerActionCtor = VirtualAction;\n    }\n    if (maxFrames === void 0) {\n      maxFrames = Infinity;\n    }\n    var _this = _super.call(this, schedulerActionCtor, function () {\n      return _this.frame;\n    }) || this;\n    _this.maxFrames = maxFrames;\n    _this.frame = 0;\n    _this.index = -1;\n    return _this;\n  }\n  VirtualTimeScheduler.prototype.flush = function () {\n    var _a = this,\n      actions = _a.actions,\n      maxFrames = _a.maxFrames;\n    var error;\n    var action;\n    while ((action = actions[0]) && action.delay <= maxFrames) {\n      actions.shift();\n      this.frame = action.delay;\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    }\n    if (error) {\n      while (action = actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  };\n  VirtualTimeScheduler.frameTimeFactor = 10;\n  return VirtualTimeScheduler;\n}(AsyncScheduler);\nexport { VirtualTimeScheduler };\nvar VirtualAction = function (_super) {\n  __extends(VirtualAction, _super);\n  function VirtualAction(scheduler, work, index) {\n    if (index === void 0) {\n      index = scheduler.index += 1;\n    }\n    var _this = _super.call(this, scheduler, work) || this;\n    _this.scheduler = scheduler;\n    _this.work = work;\n    _this.index = index;\n    _this.active = true;\n    _this.index = scheduler.index = index;\n    return _this;\n  }\n  VirtualAction.prototype.schedule = function (state, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (Number.isFinite(delay)) {\n      if (!this.id) {\n        return _super.prototype.schedule.call(this, state, delay);\n      }\n      this.active = false;\n      var action = new VirtualAction(this.scheduler, this.work);\n      this.add(action);\n      return action.schedule(state, delay);\n    } else {\n      return Subscription.EMPTY;\n    }\n  };\n  VirtualAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    this.delay = scheduler.frame + delay;\n    var actions = scheduler.actions;\n    actions.push(this);\n    actions.sort(VirtualAction.sortActions);\n    return 1;\n  };\n  VirtualAction.prototype.recycleAsyncId = function (scheduler, id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    return undefined;\n  };\n  VirtualAction.prototype._execute = function (state, delay) {\n    if (this.active === true) {\n      return _super.prototype._execute.call(this, state, delay);\n    }\n  };\n  VirtualAction.sortActions = function (a, b) {\n    if (a.delay === b.delay) {\n      if (a.index === b.index) {\n        return 0;\n      } else if (a.index > b.index) {\n        return 1;\n      } else {\n        return -1;\n      }\n    } else if (a.delay > b.delay) {\n      return 1;\n    } else {\n      return -1;\n    }\n  };\n  return VirtualAction;\n}(AsyncAction);\nexport { VirtualAction };\n", "import { Observable } from '../Observable';\nimport { isFunction } from './isFunction';\nexport function isObservable(obj) {\n  return !!obj && (obj instanceof Observable || isFunction(obj.lift) && isFunction(obj.subscribe));\n}\n", "import { EmptyError } from './util/EmptyError';\nexport function lastValueFrom(source, config) {\n  var hasConfig = typeof config === 'object';\n  return new Promise(function (resolve, reject) {\n    var _hasValue = false;\n    var _value;\n    source.subscribe({\n      next: function (value) {\n        _value = value;\n        _hasValue = true;\n      },\n      error: reject,\n      complete: function () {\n        if (_hasValue) {\n          resolve(_value);\n        } else if (hasConfig) {\n          resolve(config.defaultValue);\n        } else {\n          reject(new EmptyError());\n        }\n      }\n    });\n  });\n}\n", "import { EmptyError } from './util/EmptyError';\nimport { SafeSubscriber } from './Subscriber';\nexport function firstValueFrom(source, config) {\n  var hasConfig = typeof config === 'object';\n  return new Promise(function (resolve, reject) {\n    var subscriber = new SafeSubscriber({\n      next: function (value) {\n        resolve(value);\n        subscriber.unsubscribe();\n      },\n      error: reject,\n      complete: function () {\n        if (hasConfig) {\n          resolve(config.defaultValue);\n        } else {\n          reject(new EmptyError());\n        }\n      }\n    });\n    source.subscribe(subscriber);\n  });\n}\n", "import { __read, __spreadArray } from \"tslib\";\nimport { isScheduler } from '../util/isScheduler';\nimport { Observable } from '../Observable';\nimport { subscribeOn } from '../operators/subscribeOn';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { observeOn } from '../operators/observeOn';\nimport { AsyncSubject } from '../AsyncSubject';\nexport function bindCallbackInternals(isNodeStyle, callbackFunc, resultSelector, scheduler) {\n  if (resultSelector) {\n    if (isScheduler(resultSelector)) {\n      scheduler = resultSelector;\n    } else {\n      return function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return bindCallbackInternals(isNodeStyle, callbackFunc, scheduler).apply(this, args).pipe(mapOneOrManyArgs(resultSelector));\n      };\n    }\n  }\n  if (scheduler) {\n    return function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return bindCallbackInternals(isNodeStyle, callbackFunc).apply(this, args).pipe(subscribeOn(scheduler), observeOn(scheduler));\n    };\n  }\n  return function () {\n    var _this = this;\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    var subject = new AsyncSubject();\n    var uninitialized = true;\n    return new Observable(function (subscriber) {\n      var subs = subject.subscribe(subscriber);\n      if (uninitialized) {\n        uninitialized = false;\n        var isAsync_1 = false;\n        var isComplete_1 = false;\n        callbackFunc.apply(_this, __spreadArray(__spreadArray([], __read(args)), [function () {\n          var results = [];\n          for (var _i = 0; _i < arguments.length; _i++) {\n            results[_i] = arguments[_i];\n          }\n          if (isNodeStyle) {\n            var err = results.shift();\n            if (err != null) {\n              subject.error(err);\n              return;\n            }\n          }\n          subject.next(1 < results.length ? results : results[0]);\n          isComplete_1 = true;\n          if (isAsync_1) {\n            subject.complete();\n          }\n        }]));\n        if (isComplete_1) {\n          subject.complete();\n        }\n        isAsync_1 = true;\n      }\n      return subs;\n    });\n  };\n}\n", "import { bindCallbackInternals } from './bindCallbackInternals';\nexport function bindCallback(callbackFunc, resultSelector, scheduler) {\n  return bindCallbackInternals(false, callbackFunc, resultSelector, scheduler);\n}\n", "import { bindCallbackInternals } from './bindCallbackInternals';\nexport function bindNodeCallback(callbackFunc, resultSelector, scheduler) {\n  return bindCallbackInternals(true, callbackFunc, resultSelector, scheduler);\n}\n", "import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nexport function defer(observableFactory) {\n  return new Observable(function (subscriber) {\n    innerFrom(observableFactory()).subscribe(subscriber);\n  });\n}\n", "import { Subject } from '../Subject';\nimport { Observable } from '../Observable';\nimport { defer } from './defer';\nvar DEFAULT_CONFIG = {\n  connector: function () {\n    return new Subject();\n  },\n  resetOnDisconnect: true\n};\nexport function connectable(source, config) {\n  if (config === void 0) {\n    config = DEFAULT_CONFIG;\n  }\n  var connection = null;\n  var connector = config.connector,\n    _a = config.resetOnDisconnect,\n    resetOnDisconnect = _a === void 0 ? true : _a;\n  var subject = connector();\n  var result = new Observable(function (subscriber) {\n    return subject.subscribe(subscriber);\n  });\n  result.connect = function () {\n    if (!connection || connection.closed) {\n      connection = defer(function () {\n        return source;\n      }).subscribe(subject);\n      if (resetOnDisconnect) {\n        connection.add(function () {\n          return subject = connector();\n        });\n      }\n    }\n    return connection;\n  };\n  return result;\n}\n", "import { Observable } from '../Observable';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { innerFrom } from './innerFrom';\nimport { popResultSelector } from '../util/args';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { createObject } from '../util/createObject';\nexport function forkJoin() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var resultSelector = popResultSelector(args);\n  var _a = argsArgArrayOrObject(args),\n    sources = _a.args,\n    keys = _a.keys;\n  var result = new Observable(function (subscriber) {\n    var length = sources.length;\n    if (!length) {\n      subscriber.complete();\n      return;\n    }\n    var values = new Array(length);\n    var remainingCompletions = length;\n    var remainingEmissions = length;\n    var _loop_1 = function (sourceIndex) {\n      var hasValue = false;\n      innerFrom(sources[sourceIndex]).subscribe(createOperatorSubscriber(subscriber, function (value) {\n        if (!hasValue) {\n          hasValue = true;\n          remainingEmissions--;\n        }\n        values[sourceIndex] = value;\n      }, function () {\n        return remainingCompletions--;\n      }, undefined, function () {\n        if (!remainingCompletions || !hasValue) {\n          if (!remainingEmissions) {\n            subscriber.next(keys ? createObject(keys, values) : values);\n          }\n          subscriber.complete();\n        }\n      }));\n    };\n    for (var sourceIndex = 0; sourceIndex < length; sourceIndex++) {\n      _loop_1(sourceIndex);\n    }\n  });\n  return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n}\n", "import { __read } from \"tslib\";\nimport { innerFrom } from '../observable/innerFrom';\nimport { Observable } from '../Observable';\nimport { mergeMap } from '../operators/mergeMap';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isFunction } from '../util/isFunction';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nvar nodeEventEmitterMethods = ['addListener', 'removeListener'];\nvar eventTargetMethods = ['addEventListener', 'removeEventListener'];\nvar jqueryMethods = ['on', 'off'];\nexport function fromEvent(target, eventName, options, resultSelector) {\n  if (isFunction(options)) {\n    resultSelector = options;\n    options = undefined;\n  }\n  if (resultSelector) {\n    return fromEvent(target, eventName, options).pipe(mapOneOrManyArgs(resultSelector));\n  }\n  var _a = __read(isEventTarget(target) ? eventTargetMethods.map(function (methodName) {\n      return function (handler) {\n        return target[methodName](eventName, handler, options);\n      };\n    }) : isNodeStyleEventEmitter(target) ? nodeEventEmitterMethods.map(toCommonHandlerRegistry(target, eventName)) : isJQueryStyleEventEmitter(target) ? jqueryMethods.map(toCommonHandlerRegistry(target, eventName)) : [], 2),\n    add = _a[0],\n    remove = _a[1];\n  if (!add) {\n    if (isArrayLike(target)) {\n      return mergeMap(function (subTarget) {\n        return fromEvent(subTarget, eventName, options);\n      })(innerFrom(target));\n    }\n  }\n  if (!add) {\n    throw new TypeError('Invalid event target');\n  }\n  return new Observable(function (subscriber) {\n    var handler = function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return subscriber.next(1 < args.length ? args : args[0]);\n    };\n    add(handler);\n    return function () {\n      return remove(handler);\n    };\n  });\n}\nfunction toCommonHandlerRegistry(target, eventName) {\n  return function (methodName) {\n    return function (handler) {\n      return target[methodName](eventName, handler);\n    };\n  };\n}\nfunction isNodeStyleEventEmitter(target) {\n  return isFunction(target.addListener) && isFunction(target.removeListener);\n}\nfunction isJQueryStyleEventEmitter(target) {\n  return isFunction(target.on) && isFunction(target.off);\n}\nfunction isEventTarget(target) {\n  return isFunction(target.addEventListener) && isFunction(target.removeEventListener);\n}\n", "import { Observable } from '../Observable';\nimport { isFunction } from '../util/isFunction';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nexport function fromEventPattern(add<PERSON><PERSON><PERSON>, remove<PERSON><PERSON><PERSON>, resultSelector) {\n  if (resultSelector) {\n    return fromEventPattern(addH<PERSON><PERSON>, removeHandler).pipe(mapOneOrManyArgs(resultSelector));\n  }\n  return new Observable(function (subscriber) {\n    var handler = function () {\n      var e = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        e[_i] = arguments[_i];\n      }\n      return subscriber.next(e.length === 1 ? e[0] : e);\n    };\n    var retValue = addHandler(handler);\n    return isFunction(removeHandler) ? function () {\n      return removeHandler(handler, retValue);\n    } : undefined;\n  });\n}\n", "import { __generator } from \"tslib\";\nimport { identity } from '../util/identity';\nimport { isScheduler } from '../util/isScheduler';\nimport { defer } from './defer';\nimport { scheduleIterable } from '../scheduled/scheduleIterable';\nexport function generate(initialStateOrOptions, condition, iterate, resultSelectorOrScheduler, scheduler) {\n  var _a, _b;\n  var resultSelector;\n  var initialState;\n  if (arguments.length === 1) {\n    _a = initialStateOrOptions, initialState = _a.initialState, condition = _a.condition, iterate = _a.iterate, _b = _a.resultSelector, resultSelector = _b === void 0 ? identity : _b, scheduler = _a.scheduler;\n  } else {\n    initialState = initialStateOrOptions;\n    if (!resultSelectorOrScheduler || isScheduler(resultSelectorOrScheduler)) {\n      resultSelector = identity;\n      scheduler = resultSelectorOrScheduler;\n    } else {\n      resultSelector = resultSelectorOrScheduler;\n    }\n  }\n  function gen() {\n    var state;\n    return __generator(this, function (_a) {\n      switch (_a.label) {\n        case 0:\n          state = initialState;\n          _a.label = 1;\n        case 1:\n          if (!(!condition || condition(state))) return [3, 4];\n          return [4, resultSelector(state)];\n        case 2:\n          _a.sent();\n          _a.label = 3;\n        case 3:\n          state = iterate(state);\n          return [3, 1];\n        case 4:\n          return [2];\n      }\n    });\n  }\n  return defer(scheduler ? function () {\n    return scheduleIterable(gen(), scheduler);\n  } : gen);\n}\n", "import { defer } from './defer';\nexport function iif(condition, trueResult, falseResult) {\n  return defer(function () {\n    return condition() ? trueResult : falseResult;\n  });\n}\n", "import { mergeAll } from '../operators/mergeAll';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\nimport { popNumber, popScheduler } from '../util/args';\nimport { from } from './from';\nexport function merge() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var scheduler = popScheduler(args);\n  var concurrent = popNumber(args, Infinity);\n  var sources = args;\n  return !sources.length ? EMPTY : sources.length === 1 ? innerFrom(sources[0]) : mergeAll(concurrent)(from(sources, scheduler));\n}\n", "import { Observable } from '../Observable';\nimport { noop } from '../util/noop';\nexport var NEVER = new Observable(noop);\nexport function never() {\n  return NEVER;\n}\n", "import { from } from './from';\nexport function pairs(obj, scheduler) {\n  return from(Object.entries(obj), scheduler);\n}\n", "import { not } from '../util/not';\nimport { filter } from '../operators/filter';\nimport { innerFrom } from './innerFrom';\nexport function partition(source, predicate, thisArg) {\n  return [filter(predicate, thisArg)(innerFrom(source)), filter(not(predicate, thisArg))(innerFrom(source))];\n}\n", "import { Observable } from '../Observable';\nimport { EMPTY } from './empty';\nexport function range(start, count, scheduler) {\n  if (count == null) {\n    count = start;\n    start = 0;\n  }\n  if (count <= 0) {\n    return EMPTY;\n  }\n  var end = count + start;\n  return new Observable(scheduler ? function (subscriber) {\n    var n = start;\n    return scheduler.schedule(function () {\n      if (n < end) {\n        subscriber.next(n++);\n        this.schedule();\n      } else {\n        subscriber.complete();\n      }\n    });\n  } : function (subscriber) {\n    var n = start;\n    while (n < end && !subscriber.closed) {\n      subscriber.next(n++);\n    }\n    subscriber.complete();\n  });\n}\n", "import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\nexport function using(resourceFactory, observableFactory) {\n  return new Observable(function (subscriber) {\n    var resource = resourceFactory();\n    var result = observableFactory(resource);\n    var source = result ? innerFrom(result) : EMPTY;\n    source.subscribe(subscriber);\n    return function () {\n      if (resource) {\n        resource.unsubscribe();\n      }\n    };\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,IAAI,+BAA+B;AAAA,EACxC,KAAK,WAAY;AACf,YAAQ,6BAA6B,YAAY,aAAa,IAAI;AAAA,EACpE;AAAA,EACA,UAAU;AACZ;;;ACHO,IAAI,yBAAyB;AAAA,EAClC,UAAU,SAAU,UAAU;AAC5B,QAAI,UAAU;AACd,QAAI,SAAS;AACb,QAAI,WAAW,uBAAuB;AACtC,QAAI,UAAU;AACZ,gBAAU,SAAS;AACnB,eAAS,SAAS;AAAA,IACpB;AACA,QAAI,SAAS,QAAQ,SAAUA,YAAW;AACxC,eAAS;AACT,eAASA,UAAS;AAAA,IACpB,CAAC;AACD,WAAO,IAAI,aAAa,WAAY;AAClC,aAAO,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,MAAM;AAAA,IACtE,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB,WAAY;AACjC,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IACzB;AACA,QAAI,WAAW,uBAAuB;AACtC,aAAS,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,0BAA0B,uBAAuB,MAAM,QAAQ,cAAc,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AAAA,EACtK;AAAA,EACA,sBAAsB,WAAY;AAChC,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IACzB;AACA,QAAI,WAAW,uBAAuB;AACtC,aAAS,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,yBAAyB,sBAAsB,MAAM,QAAQ,cAAc,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AAAA,EACpK;AAAA,EACA,UAAU;AACZ;;;ACjCO,SAAS,gBAAgB,mBAAmB;AACjD,SAAO,oBAAoB,uBAAuB,iBAAiB,IAAI;AACzE;AACA,SAAS,uBAAuB,mBAAmB;AACjD,SAAO,IAAI,WAAW,SAAU,YAAY;AAC1C,QAAI,WAAW,qBAAqB;AACpC,QAAI,QAAQ,SAAS,IAAI;AACzB,QAAI,KAAK;AACT,QAAI,MAAM,WAAY;AACpB,UAAI,CAAC,WAAW,QAAQ;AACtB,aAAK,uBAAuB,sBAAsB,SAAUC,YAAW;AACrE,eAAK;AACL,cAAI,MAAM,SAAS,IAAI;AACvB,qBAAW,KAAK;AAAA,YACd,WAAW,oBAAoB,MAAMA;AAAA,YACrC,SAAS,MAAM;AAAA,UACjB,CAAC;AACD,cAAI;AAAA,QACN,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI;AACJ,WAAO,WAAY;AACjB,UAAI,IAAI;AACN,+BAAuB,qBAAqB,EAAE;AAAA,MAChD;AAAA,IACF;AAAA,EACF,CAAC;AACH;AACA,IAAI,2BAA2B,uBAAuB;;;AChCtD,IAAI,aAAa;AACjB,IAAI;AACJ,IAAI,gBAAgB,CAAC;AACrB,SAAS,mBAAmB,QAAQ;AAClC,MAAI,UAAU,eAAe;AAC3B,WAAO,cAAc,MAAM;AAC3B,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACO,IAAI,YAAY;AAAA,EACrB,cAAc,SAAU,IAAI;AAC1B,QAAI,SAAS;AACb,kBAAc,MAAM,IAAI;AACxB,QAAI,CAAC,UAAU;AACb,iBAAW,QAAQ,QAAQ;AAAA,IAC7B;AACA,aAAS,KAAK,WAAY;AACxB,aAAO,mBAAmB,MAAM,KAAK,GAAG;AAAA,IAC1C,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,SAAU,QAAQ;AAChC,uBAAmB,MAAM;AAAA,EAC3B;AACF;;;ACvBA,IAAI,eAAe,UAAU;AAA7B,IACE,iBAAiB,UAAU;AACtB,IAAI,oBAAoB;AAAA,EAC7B,cAAc,WAAY;AACxB,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IACzB;AACA,QAAI,WAAW,kBAAkB;AACjC,aAAS,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,iBAAiB,cAAc,MAAM,QAAQ,cAAc,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AAAA,EACpJ;AAAA,EACA,gBAAgB,SAAU,QAAQ;AAChC,QAAI,WAAW,kBAAkB;AACjC,aAAS,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,mBAAmB,gBAAgB,MAAM;AAAA,EACjH;AAAA,EACA,UAAU;AACZ;;;ACfA,IAAI,aAAa,SAAU,QAAQ;AACjC,YAAUC,aAAY,MAAM;AAC5B,WAASA,YAAW,WAAW,MAAM;AACnC,QAAI,QAAQ,OAAO,KAAK,MAAM,WAAW,IAAI,KAAK;AAClD,UAAM,YAAY;AAClB,UAAM,OAAO;AACb,WAAO;AAAA,EACT;AACA,EAAAA,YAAW,UAAU,iBAAiB,SAAU,WAAW,IAAIC,QAAO;AACpE,QAAIA,WAAU,QAAQ;AACpB,MAAAA,SAAQ;AAAA,IACV;AACA,QAAIA,WAAU,QAAQA,SAAQ,GAAG;AAC/B,aAAO,OAAO,UAAU,eAAe,KAAK,MAAM,WAAW,IAAIA,MAAK;AAAA,IACxE;AACA,cAAU,QAAQ,KAAK,IAAI;AAC3B,WAAO,UAAU,eAAe,UAAU,aAAa,kBAAkB,aAAa,UAAU,MAAM,KAAK,WAAW,MAAS,CAAC;AAAA,EAClI;AACA,EAAAD,YAAW,UAAU,iBAAiB,SAAU,WAAW,IAAIC,QAAO;AACpE,QAAI;AACJ,QAAIA,WAAU,QAAQ;AACpB,MAAAA,SAAQ;AAAA,IACV;AACA,QAAIA,UAAS,OAAOA,SAAQ,IAAI,KAAK,QAAQ,GAAG;AAC9C,aAAO,OAAO,UAAU,eAAe,KAAK,MAAM,WAAW,IAAIA,MAAK;AAAA,IACxE;AACA,QAAI,UAAU,UAAU;AACxB,QAAI,MAAM,UAAU,KAAK,QAAQ,QAAQ,SAAS,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,IAAI;AACxG,wBAAkB,eAAe,EAAE;AACnC,UAAI,UAAU,eAAe,IAAI;AAC/B,kBAAU,aAAa;AAAA,MACzB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAOD;AACT,EAAE,WAAW;;;ACrCb,IAAI,gBAAgB,SAAU,QAAQ;AACpC,YAAUE,gBAAe,MAAM;AAC/B,WAASA,iBAAgB;AACvB,WAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,EAC7D;AACA,EAAAA,eAAc,UAAU,QAAQ,SAAU,QAAQ;AAChD,SAAK,UAAU;AACf,QAAI,UAAU,KAAK;AACnB,SAAK,aAAa;AAClB,QAAI,UAAU,KAAK;AACnB,QAAI;AACJ,aAAS,UAAU,QAAQ,MAAM;AACjC,OAAG;AACD,UAAI,QAAQ,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK,GAAG;AACtD;AAAA,MACF;AAAA,IACF,UAAU,SAAS,QAAQ,CAAC,MAAM,OAAO,OAAO,WAAW,QAAQ,MAAM;AACzE,SAAK,UAAU;AACf,QAAI,OAAO;AACT,cAAQ,SAAS,QAAQ,CAAC,MAAM,OAAO,OAAO,WAAW,QAAQ,MAAM,GAAG;AACxE,eAAO,YAAY;AAAA,MACrB;AACA,YAAM;AAAA,IACR;AAAA,EACF;AACA,SAAOA;AACT,EAAE,cAAc;;;AC1BT,IAAI,gBAAgB,IAAI,cAAc,UAAU;AAChD,IAAI,OAAO;;;ACDlB,IAAI,cAAc,SAAU,QAAQ;AAClC,YAAUC,cAAa,MAAM;AAC7B,WAASA,aAAY,WAAW,MAAM;AACpC,QAAI,QAAQ,OAAO,KAAK,MAAM,WAAW,IAAI,KAAK;AAClD,UAAM,YAAY;AAClB,UAAM,OAAO;AACb,WAAO;AAAA,EACT;AACA,EAAAA,aAAY,UAAU,WAAW,SAAU,OAAOC,QAAO;AACvD,QAAIA,WAAU,QAAQ;AACpB,MAAAA,SAAQ;AAAA,IACV;AACA,QAAIA,SAAQ,GAAG;AACb,aAAO,OAAO,UAAU,SAAS,KAAK,MAAM,OAAOA,MAAK;AAAA,IAC1D;AACA,SAAK,QAAQA;AACb,SAAK,QAAQ;AACb,SAAK,UAAU,MAAM,IAAI;AACzB,WAAO;AAAA,EACT;AACA,EAAAD,aAAY,UAAU,UAAU,SAAU,OAAOC,QAAO;AACtD,WAAOA,SAAQ,KAAK,KAAK,SAAS,OAAO,UAAU,QAAQ,KAAK,MAAM,OAAOA,MAAK,IAAI,KAAK,SAAS,OAAOA,MAAK;AAAA,EAClH;AACA,EAAAD,aAAY,UAAU,iBAAiB,SAAU,WAAW,IAAIC,QAAO;AACrE,QAAIA,WAAU,QAAQ;AACpB,MAAAA,SAAQ;AAAA,IACV;AACA,QAAIA,UAAS,QAAQA,SAAQ,KAAKA,UAAS,QAAQ,KAAK,QAAQ,GAAG;AACjE,aAAO,OAAO,UAAU,eAAe,KAAK,MAAM,WAAW,IAAIA,MAAK;AAAA,IACxE;AACA,cAAU,MAAM,IAAI;AACpB,WAAO;AAAA,EACT;AACA,SAAOD;AACT,EAAE,WAAW;;;AClCb,IAAI,iBAAiB,SAAU,QAAQ;AACrC,YAAUE,iBAAgB,MAAM;AAChC,WAASA,kBAAiB;AACxB,WAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,EAC7D;AACA,SAAOA;AACT,EAAE,cAAc;;;ACNT,IAAI,iBAAiB,IAAI,eAAe,WAAW;AACnD,IAAI,QAAQ;;;ACAnB,IAAI,uBAAuB,SAAU,QAAQ;AAC3C,YAAUC,uBAAsB,MAAM;AACtC,WAASA,sBAAqB,WAAW,MAAM;AAC7C,QAAI,QAAQ,OAAO,KAAK,MAAM,WAAW,IAAI,KAAK;AAClD,UAAM,YAAY;AAClB,UAAM,OAAO;AACb,WAAO;AAAA,EACT;AACA,EAAAA,sBAAqB,UAAU,iBAAiB,SAAU,WAAW,IAAIC,QAAO;AAC9E,QAAIA,WAAU,QAAQ;AACpB,MAAAA,SAAQ;AAAA,IACV;AACA,QAAIA,WAAU,QAAQA,SAAQ,GAAG;AAC/B,aAAO,OAAO,UAAU,eAAe,KAAK,MAAM,WAAW,IAAIA,MAAK;AAAA,IACxE;AACA,cAAU,QAAQ,KAAK,IAAI;AAC3B,WAAO,UAAU,eAAe,UAAU,aAAa,uBAAuB,sBAAsB,WAAY;AAC9G,aAAO,UAAU,MAAM,MAAS;AAAA,IAClC,CAAC;AAAA,EACH;AACA,EAAAD,sBAAqB,UAAU,iBAAiB,SAAU,WAAW,IAAIC,QAAO;AAC9E,QAAI;AACJ,QAAIA,WAAU,QAAQ;AACpB,MAAAA,SAAQ;AAAA,IACV;AACA,QAAIA,UAAS,OAAOA,SAAQ,IAAI,KAAK,QAAQ,GAAG;AAC9C,aAAO,OAAO,UAAU,eAAe,KAAK,MAAM,WAAW,IAAIA,MAAK;AAAA,IACxE;AACA,QAAI,UAAU,UAAU;AACxB,QAAI,MAAM,QAAQ,OAAO,UAAU,gBAAgB,KAAK,QAAQ,QAAQ,SAAS,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,IAAI;AACvI,6BAAuB,qBAAqB,EAAE;AAC9C,gBAAU,aAAa;AAAA,IACzB;AACA,WAAO;AAAA,EACT;AACA,SAAOD;AACT,EAAE,WAAW;;;ACrCb,IAAI,0BAA0B,SAAU,QAAQ;AAC9C,YAAUE,0BAAyB,MAAM;AACzC,WAASA,2BAA0B;AACjC,WAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,EAC7D;AACA,EAAAA,yBAAwB,UAAU,QAAQ,SAAU,QAAQ;AAC1D,SAAK,UAAU;AACf,QAAI;AACJ,QAAI,QAAQ;AACV,gBAAU,OAAO;AAAA,IACnB,OAAO;AACL,gBAAU,KAAK;AACf,WAAK,aAAa;AAAA,IACpB;AACA,QAAI,UAAU,KAAK;AACnB,QAAI;AACJ,aAAS,UAAU,QAAQ,MAAM;AACjC,OAAG;AACD,UAAI,QAAQ,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK,GAAG;AACtD;AAAA,MACF;AAAA,IACF,UAAU,SAAS,QAAQ,CAAC,MAAM,OAAO,OAAO,WAAW,QAAQ,MAAM;AACzE,SAAK,UAAU;AACf,QAAI,OAAO;AACT,cAAQ,SAAS,QAAQ,CAAC,MAAM,OAAO,OAAO,WAAW,QAAQ,MAAM,GAAG;AACxE,eAAO,YAAY;AAAA,MACrB;AACA,YAAM;AAAA,IACR;AAAA,EACF;AACA,SAAOA;AACT,EAAE,cAAc;;;AC/BT,IAAI,0BAA0B,IAAI,wBAAwB,oBAAoB;AAC9E,IAAI,iBAAiB;;;ACC5B,IAAI,uBAAuB,SAAU,QAAQ;AAC3C,YAAUC,uBAAsB,MAAM;AACtC,WAASA,sBAAqB,qBAAqB,WAAW;AAC5D,QAAI,wBAAwB,QAAQ;AAClC,4BAAsB;AAAA,IACxB;AACA,QAAI,cAAc,QAAQ;AACxB,kBAAY;AAAA,IACd;AACA,QAAI,QAAQ,OAAO,KAAK,MAAM,qBAAqB,WAAY;AAC7D,aAAO,MAAM;AAAA,IACf,CAAC,KAAK;AACN,UAAM,YAAY;AAClB,UAAM,QAAQ;AACd,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AACA,EAAAA,sBAAqB,UAAU,QAAQ,WAAY;AACjD,QAAI,KAAK,MACP,UAAU,GAAG,SACb,YAAY,GAAG;AACjB,QAAI;AACJ,QAAI;AACJ,YAAQ,SAAS,QAAQ,CAAC,MAAM,OAAO,SAAS,WAAW;AACzD,cAAQ,MAAM;AACd,WAAK,QAAQ,OAAO;AACpB,UAAI,QAAQ,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK,GAAG;AACtD;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO;AACT,aAAO,SAAS,QAAQ,MAAM,GAAG;AAC/B,eAAO,YAAY;AAAA,MACrB;AACA,YAAM;AAAA,IACR;AAAA,EACF;AACA,EAAAA,sBAAqB,kBAAkB;AACvC,SAAOA;AACT,EAAE,cAAc;AAEhB,IAAI,gBAAgB,SAAU,QAAQ;AACpC,YAAUC,gBAAe,MAAM;AAC/B,WAASA,eAAc,WAAW,MAAM,OAAO;AAC7C,QAAI,UAAU,QAAQ;AACpB,cAAQ,UAAU,SAAS;AAAA,IAC7B;AACA,QAAI,QAAQ,OAAO,KAAK,MAAM,WAAW,IAAI,KAAK;AAClD,UAAM,YAAY;AAClB,UAAM,OAAO;AACb,UAAM,QAAQ;AACd,UAAM,SAAS;AACf,UAAM,QAAQ,UAAU,QAAQ;AAChC,WAAO;AAAA,EACT;AACA,EAAAA,eAAc,UAAU,WAAW,SAAU,OAAOC,QAAO;AACzD,QAAIA,WAAU,QAAQ;AACpB,MAAAA,SAAQ;AAAA,IACV;AACA,QAAI,OAAO,SAASA,MAAK,GAAG;AAC1B,UAAI,CAAC,KAAK,IAAI;AACZ,eAAO,OAAO,UAAU,SAAS,KAAK,MAAM,OAAOA,MAAK;AAAA,MAC1D;AACA,WAAK,SAAS;AACd,UAAI,SAAS,IAAID,eAAc,KAAK,WAAW,KAAK,IAAI;AACxD,WAAK,IAAI,MAAM;AACf,aAAO,OAAO,SAAS,OAAOC,MAAK;AAAA,IACrC,OAAO;AACL,aAAO,aAAa;AAAA,IACtB;AAAA,EACF;AACA,EAAAD,eAAc,UAAU,iBAAiB,SAAU,WAAW,IAAIC,QAAO;AACvE,QAAIA,WAAU,QAAQ;AACpB,MAAAA,SAAQ;AAAA,IACV;AACA,SAAK,QAAQ,UAAU,QAAQA;AAC/B,QAAI,UAAU,UAAU;AACxB,YAAQ,KAAK,IAAI;AACjB,YAAQ,KAAKD,eAAc,WAAW;AACtC,WAAO;AAAA,EACT;AACA,EAAAA,eAAc,UAAU,iBAAiB,SAAU,WAAW,IAAIC,QAAO;AACvE,QAAIA,WAAU,QAAQ;AACpB,MAAAA,SAAQ;AAAA,IACV;AACA,WAAO;AAAA,EACT;AACA,EAAAD,eAAc,UAAU,WAAW,SAAU,OAAOC,QAAO;AACzD,QAAI,KAAK,WAAW,MAAM;AACxB,aAAO,OAAO,UAAU,SAAS,KAAK,MAAM,OAAOA,MAAK;AAAA,IAC1D;AAAA,EACF;AACA,EAAAD,eAAc,cAAc,SAAU,GAAG,GAAG;AAC1C,QAAI,EAAE,UAAU,EAAE,OAAO;AACvB,UAAI,EAAE,UAAU,EAAE,OAAO;AACvB,eAAO;AAAA,MACT,WAAW,EAAE,QAAQ,EAAE,OAAO;AAC5B,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,WAAW,EAAE,QAAQ,EAAE,OAAO;AAC5B,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAOA;AACT,EAAE,WAAW;;;AC9GN,SAAS,aAAa,KAAK;AAChC,SAAO,CAAC,CAAC,QAAQ,eAAe,cAAc,WAAW,IAAI,IAAI,KAAK,WAAW,IAAI,SAAS;AAChG;;;ACHO,SAAS,cAAc,QAAQE,SAAQ;AAC5C,MAAI,YAAY,OAAOA,YAAW;AAClC,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,QAAI,YAAY;AAChB,QAAI;AACJ,WAAO,UAAU;AAAA,MACf,MAAM,SAAU,OAAO;AACrB,iBAAS;AACT,oBAAY;AAAA,MACd;AAAA,MACA,OAAO;AAAA,MACP,UAAU,WAAY;AACpB,YAAI,WAAW;AACb,kBAAQ,MAAM;AAAA,QAChB,WAAW,WAAW;AACpB,kBAAQA,QAAO,YAAY;AAAA,QAC7B,OAAO;AACL,iBAAO,IAAI,WAAW,CAAC;AAAA,QACzB;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;;;ACrBO,SAAS,eAAe,QAAQC,SAAQ;AAC7C,MAAI,YAAY,OAAOA,YAAW;AAClC,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,QAAI,aAAa,IAAI,eAAe;AAAA,MAClC,MAAM,SAAU,OAAO;AACrB,gBAAQ,KAAK;AACb,mBAAW,YAAY;AAAA,MACzB;AAAA,MACA,OAAO;AAAA,MACP,UAAU,WAAY;AACpB,YAAI,WAAW;AACb,kBAAQA,QAAO,YAAY;AAAA,QAC7B,OAAO;AACL,iBAAO,IAAI,WAAW,CAAC;AAAA,QACzB;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO,UAAU,UAAU;AAAA,EAC7B,CAAC;AACH;;;ACdO,SAAS,sBAAsB,aAAa,cAAc,gBAAgB,WAAW;AAC1F,MAAI,gBAAgB;AAClB,QAAI,YAAY,cAAc,GAAG;AAC/B,kBAAY;AAAA,IACd,OAAO;AACL,aAAO,WAAY;AACjB,YAAI,OAAO,CAAC;AACZ,iBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,eAAK,EAAE,IAAI,UAAU,EAAE;AAAA,QACzB;AACA,eAAO,sBAAsB,aAAa,cAAc,SAAS,EAAE,MAAM,MAAM,IAAI,EAAE,KAAK,iBAAiB,cAAc,CAAC;AAAA,MAC5H;AAAA,IACF;AAAA,EACF;AACA,MAAI,WAAW;AACb,WAAO,WAAY;AACjB,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MACzB;AACA,aAAO,sBAAsB,aAAa,YAAY,EAAE,MAAM,MAAM,IAAI,EAAE,KAAK,YAAY,SAAS,GAAG,UAAU,SAAS,CAAC;AAAA,IAC7H;AAAA,EACF;AACA,SAAO,WAAY;AACjB,QAAI,QAAQ;AACZ,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IACzB;AACA,QAAI,UAAU,IAAI,aAAa;AAC/B,QAAI,gBAAgB;AACpB,WAAO,IAAI,WAAW,SAAU,YAAY;AAC1C,UAAI,OAAO,QAAQ,UAAU,UAAU;AACvC,UAAI,eAAe;AACjB,wBAAgB;AAChB,YAAI,YAAY;AAChB,YAAI,eAAe;AACnB,qBAAa,MAAM,OAAO,cAAc,cAAc,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,CAAC,WAAY;AACpF,cAAI,UAAU,CAAC;AACf,mBAASC,MAAK,GAAGA,MAAK,UAAU,QAAQA,OAAM;AAC5C,oBAAQA,GAAE,IAAI,UAAUA,GAAE;AAAA,UAC5B;AACA,cAAI,aAAa;AACf,gBAAI,MAAM,QAAQ,MAAM;AACxB,gBAAI,OAAO,MAAM;AACf,sBAAQ,MAAM,GAAG;AACjB;AAAA,YACF;AAAA,UACF;AACA,kBAAQ,KAAK,IAAI,QAAQ,SAAS,UAAU,QAAQ,CAAC,CAAC;AACtD,yBAAe;AACf,cAAI,WAAW;AACb,oBAAQ,SAAS;AAAA,UACnB;AAAA,QACF,CAAC,CAAC,CAAC;AACH,YAAI,cAAc;AAChB,kBAAQ,SAAS;AAAA,QACnB;AACA,oBAAY;AAAA,MACd;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;;;ACrEO,SAAS,aAAa,cAAc,gBAAgB,WAAW;AACpE,SAAO,sBAAsB,OAAO,cAAc,gBAAgB,SAAS;AAC7E;;;ACFO,SAAS,iBAAiB,cAAc,gBAAgB,WAAW;AACxE,SAAO,sBAAsB,MAAM,cAAc,gBAAgB,SAAS;AAC5E;;;ACDO,SAAS,MAAM,mBAAmB;AACvC,SAAO,IAAI,WAAW,SAAU,YAAY;AAC1C,cAAU,kBAAkB,CAAC,EAAE,UAAU,UAAU;AAAA,EACrD,CAAC;AACH;;;ACHA,IAAI,iBAAiB;AAAA,EACnB,WAAW,WAAY;AACrB,WAAO,IAAI,QAAQ;AAAA,EACrB;AAAA,EACA,mBAAmB;AACrB;AACO,SAAS,YAAY,QAAQC,SAAQ;AAC1C,MAAIA,YAAW,QAAQ;AACrB,IAAAA,UAAS;AAAA,EACX;AACA,MAAI,aAAa;AACjB,MAAI,YAAYA,QAAO,WACrB,KAAKA,QAAO,mBACZ,oBAAoB,OAAO,SAAS,OAAO;AAC7C,MAAI,UAAU,UAAU;AACxB,MAAI,SAAS,IAAI,WAAW,SAAU,YAAY;AAChD,WAAO,QAAQ,UAAU,UAAU;AAAA,EACrC,CAAC;AACD,SAAO,UAAU,WAAY;AAC3B,QAAI,CAAC,cAAc,WAAW,QAAQ;AACpC,mBAAa,MAAM,WAAY;AAC7B,eAAO;AAAA,MACT,CAAC,EAAE,UAAU,OAAO;AACpB,UAAI,mBAAmB;AACrB,mBAAW,IAAI,WAAY;AACzB,iBAAO,UAAU,UAAU;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;;;AC5BO,SAAS,WAAW;AACzB,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EACzB;AACA,MAAI,iBAAiB,kBAAkB,IAAI;AAC3C,MAAI,KAAK,qBAAqB,IAAI,GAChC,UAAU,GAAG,MACb,OAAO,GAAG;AACZ,MAAI,SAAS,IAAI,WAAW,SAAU,YAAY;AAChD,QAAI,SAAS,QAAQ;AACrB,QAAI,CAAC,QAAQ;AACX,iBAAW,SAAS;AACpB;AAAA,IACF;AACA,QAAI,SAAS,IAAI,MAAM,MAAM;AAC7B,QAAI,uBAAuB;AAC3B,QAAI,qBAAqB;AACzB,QAAI,UAAU,SAAUC,cAAa;AACnC,UAAI,WAAW;AACf,gBAAU,QAAQA,YAAW,CAAC,EAAE,UAAU,yBAAyB,YAAY,SAAU,OAAO;AAC9F,YAAI,CAAC,UAAU;AACb,qBAAW;AACX;AAAA,QACF;AACA,eAAOA,YAAW,IAAI;AAAA,MACxB,GAAG,WAAY;AACb,eAAO;AAAA,MACT,GAAG,QAAW,WAAY;AACxB,YAAI,CAAC,wBAAwB,CAAC,UAAU;AACtC,cAAI,CAAC,oBAAoB;AACvB,uBAAW,KAAK,OAAO,aAAa,MAAM,MAAM,IAAI,MAAM;AAAA,UAC5D;AACA,qBAAW,SAAS;AAAA,QACtB;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AACA,aAAS,cAAc,GAAG,cAAc,QAAQ,eAAe;AAC7D,cAAQ,WAAW;AAAA,IACrB;AAAA,EACF,CAAC;AACD,SAAO,iBAAiB,OAAO,KAAK,iBAAiB,cAAc,CAAC,IAAI;AAC1E;;;AC1CA,IAAI,0BAA0B,CAAC,eAAe,gBAAgB;AAC9D,IAAI,qBAAqB,CAAC,oBAAoB,qBAAqB;AACnE,IAAI,gBAAgB,CAAC,MAAM,KAAK;AACzB,SAAS,UAAU,QAAQ,WAAW,SAAS,gBAAgB;AACpE,MAAI,WAAW,OAAO,GAAG;AACvB,qBAAiB;AACjB,cAAU;AAAA,EACZ;AACA,MAAI,gBAAgB;AAClB,WAAO,UAAU,QAAQ,WAAW,OAAO,EAAE,KAAK,iBAAiB,cAAc,CAAC;AAAA,EACpF;AACA,MAAI,KAAK,OAAO,cAAc,MAAM,IAAI,mBAAmB,IAAI,SAAU,YAAY;AACjF,WAAO,SAAU,SAAS;AACxB,aAAO,OAAO,UAAU,EAAE,WAAW,SAAS,OAAO;AAAA,IACvD;AAAA,EACF,CAAC,IAAI,wBAAwB,MAAM,IAAI,wBAAwB,IAAI,wBAAwB,QAAQ,SAAS,CAAC,IAAI,0BAA0B,MAAM,IAAI,cAAc,IAAI,wBAAwB,QAAQ,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,GAC1N,MAAM,GAAG,CAAC,GACV,SAAS,GAAG,CAAC;AACf,MAAI,CAAC,KAAK;AACR,QAAI,YAAY,MAAM,GAAG;AACvB,aAAO,SAAS,SAAU,WAAW;AACnC,eAAO,UAAU,WAAW,WAAW,OAAO;AAAA,MAChD,CAAC,EAAE,UAAU,MAAM,CAAC;AAAA,IACtB;AAAA,EACF;AACA,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,UAAU,sBAAsB;AAAA,EAC5C;AACA,SAAO,IAAI,WAAW,SAAU,YAAY;AAC1C,QAAI,UAAU,WAAY;AACxB,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MACzB;AACA,aAAO,WAAW,KAAK,IAAI,KAAK,SAAS,OAAO,KAAK,CAAC,CAAC;AAAA,IACzD;AACA,QAAI,OAAO;AACX,WAAO,WAAY;AACjB,aAAO,OAAO,OAAO;AAAA,IACvB;AAAA,EACF,CAAC;AACH;AACA,SAAS,wBAAwB,QAAQ,WAAW;AAClD,SAAO,SAAU,YAAY;AAC3B,WAAO,SAAU,SAAS;AACxB,aAAO,OAAO,UAAU,EAAE,WAAW,OAAO;AAAA,IAC9C;AAAA,EACF;AACF;AACA,SAAS,wBAAwB,QAAQ;AACvC,SAAO,WAAW,OAAO,WAAW,KAAK,WAAW,OAAO,cAAc;AAC3E;AACA,SAAS,0BAA0B,QAAQ;AACzC,SAAO,WAAW,OAAO,EAAE,KAAK,WAAW,OAAO,GAAG;AACvD;AACA,SAAS,cAAc,QAAQ;AAC7B,SAAO,WAAW,OAAO,gBAAgB,KAAK,WAAW,OAAO,mBAAmB;AACrF;;;AC7DO,SAAS,iBAAiB,YAAY,eAAe,gBAAgB;AAC1E,MAAI,gBAAgB;AAClB,WAAO,iBAAiB,YAAY,aAAa,EAAE,KAAK,iBAAiB,cAAc,CAAC;AAAA,EAC1F;AACA,SAAO,IAAI,WAAW,SAAU,YAAY;AAC1C,QAAI,UAAU,WAAY;AACxB,UAAI,IAAI,CAAC;AACT,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,UAAE,EAAE,IAAI,UAAU,EAAE;AAAA,MACtB;AACA,aAAO,WAAW,KAAK,EAAE,WAAW,IAAI,EAAE,CAAC,IAAI,CAAC;AAAA,IAClD;AACA,QAAI,WAAW,WAAW,OAAO;AACjC,WAAO,WAAW,aAAa,IAAI,WAAY;AAC7C,aAAO,cAAc,SAAS,QAAQ;AAAA,IACxC,IAAI;AAAA,EACN,CAAC;AACH;;;ACfO,SAAS,SAAS,uBAAuB,WAAW,SAAS,2BAA2B,WAAW;AACxG,MAAI,IAAI;AACR,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU,WAAW,GAAG;AAC1B,SAAK,uBAAuB,eAAe,GAAG,cAAc,YAAY,GAAG,WAAW,UAAU,GAAG,SAAS,KAAK,GAAG,gBAAgB,iBAAiB,OAAO,SAAS,WAAW,IAAI,YAAY,GAAG;AAAA,EACrM,OAAO;AACL,mBAAe;AACf,QAAI,CAAC,6BAA6B,YAAY,yBAAyB,GAAG;AACxE,uBAAiB;AACjB,kBAAY;AAAA,IACd,OAAO;AACL,uBAAiB;AAAA,IACnB;AAAA,EACF;AACA,WAAS,MAAM;AACb,QAAI;AACJ,WAAO,YAAY,MAAM,SAAUC,KAAI;AACrC,cAAQA,IAAG,OAAO;AAAA,QAChB,KAAK;AACH,kBAAQ;AACR,UAAAA,IAAG,QAAQ;AAAA,QACb,KAAK;AACH,cAAI,EAAE,CAAC,aAAa,UAAU,KAAK,GAAI,QAAO,CAAC,GAAG,CAAC;AACnD,iBAAO,CAAC,GAAG,eAAe,KAAK,CAAC;AAAA,QAClC,KAAK;AACH,UAAAA,IAAG,KAAK;AACR,UAAAA,IAAG,QAAQ;AAAA,QACb,KAAK;AACH,kBAAQ,QAAQ,KAAK;AACrB,iBAAO,CAAC,GAAG,CAAC;AAAA,QACd,KAAK;AACH,iBAAO,CAAC,CAAC;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO,MAAM,YAAY,WAAY;AACnC,WAAO,iBAAiB,IAAI,GAAG,SAAS;AAAA,EAC1C,IAAI,GAAG;AACT;;;AC3CO,SAAS,IAAI,WAAW,YAAY,aAAa;AACtD,SAAO,MAAM,WAAY;AACvB,WAAO,UAAU,IAAI,aAAa;AAAA,EACpC,CAAC;AACH;;;ACAO,SAAS,QAAQ;AACtB,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EACzB;AACA,MAAI,YAAY,aAAa,IAAI;AACjC,MAAI,aAAa,UAAU,MAAM,QAAQ;AACzC,MAAI,UAAU;AACd,SAAO,CAAC,QAAQ,SAAS,QAAQ,QAAQ,WAAW,IAAI,UAAU,QAAQ,CAAC,CAAC,IAAI,SAAS,UAAU,EAAE,KAAK,SAAS,SAAS,CAAC;AAC/H;;;ACZO,IAAI,QAAQ,IAAI,WAAW,IAAI;AAC/B,SAAS,QAAQ;AACtB,SAAO;AACT;;;ACJO,SAAS,MAAM,KAAK,WAAW;AACpC,SAAO,KAAK,OAAO,QAAQ,GAAG,GAAG,SAAS;AAC5C;;;ACAO,SAAS,UAAU,QAAQ,WAAW,SAAS;AACpD,SAAO,CAAC,OAAO,WAAW,OAAO,EAAE,UAAU,MAAM,CAAC,GAAG,OAAO,IAAI,WAAW,OAAO,CAAC,EAAE,UAAU,MAAM,CAAC,CAAC;AAC3G;;;ACHO,SAAS,MAAM,OAAOC,QAAO,WAAW;AAC7C,MAAIA,UAAS,MAAM;AACjB,IAAAA,SAAQ;AACR,YAAQ;AAAA,EACV;AACA,MAAIA,UAAS,GAAG;AACd,WAAO;AAAA,EACT;AACA,MAAI,MAAMA,SAAQ;AAClB,SAAO,IAAI,WAAW,YAAY,SAAU,YAAY;AACtD,QAAI,IAAI;AACR,WAAO,UAAU,SAAS,WAAY;AACpC,UAAI,IAAI,KAAK;AACX,mBAAW,KAAK,GAAG;AACnB,aAAK,SAAS;AAAA,MAChB,OAAO;AACL,mBAAW,SAAS;AAAA,MACtB;AAAA,IACF,CAAC;AAAA,EACH,IAAI,SAAU,YAAY;AACxB,QAAI,IAAI;AACR,WAAO,IAAI,OAAO,CAAC,WAAW,QAAQ;AACpC,iBAAW,KAAK,GAAG;AAAA,IACrB;AACA,eAAW,SAAS;AAAA,EACtB,CAAC;AACH;;;ACzBO,SAAS,MAAM,iBAAiB,mBAAmB;AACxD,SAAO,IAAI,WAAW,SAAU,YAAY;AAC1C,QAAI,WAAW,gBAAgB;AAC/B,QAAI,SAAS,kBAAkB,QAAQ;AACvC,QAAI,SAAS,SAAS,UAAU,MAAM,IAAI;AAC1C,WAAO,UAAU,UAAU;AAC3B,WAAO,WAAY;AACjB,UAAI,UAAU;AACZ,iBAAS,YAAY;AAAA,MACvB;AAAA,IACF;AAAA,EACF,CAAC;AACH;", "names": ["timestamp", "timestamp", "AsapAction", "delay", "AsapScheduler", "QueueAction", "delay", "QueueScheduler", "AnimationFrameAction", "delay", "AnimationFrameScheduler", "VirtualTimeScheduler", "VirtualAction", "delay", "config", "config", "_i", "config", "sourceIndex", "_a", "count"]}