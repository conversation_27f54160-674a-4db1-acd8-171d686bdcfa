.user-detail-container {
  padding: 2rem 0;
  min-height: 60vh;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.back-btn {
  margin-bottom: 2rem;
}

.user-profile {
  margin-top: 1rem;
}

.profile-layout {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
}

.user-avatar img {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #e9ecef;
}

.user-info {
  flex: 1;
}

.user-info h1 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 2rem;
}

.user-id {
  color: #6c757d;
  font-size: 1rem;
  margin-bottom: 1.5rem;
}

.user-details p {
  margin: 0.5rem 0;
  font-size: 1rem;
  line-height: 1.5;
}

.user-details strong {
  color: #333;
  min-width: 80px;
  display: inline-block;
}

.address-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.address-details {
  margin-left: 80px;
  color: #6c757d;
  line-height: 1.4;
}

.user-not-found {
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-not-found h2 {
  color: #dc3545;
  margin-bottom: 1rem;
}

.user-not-found p {
  color: #6c757d;
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .profile-layout {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .user-avatar img {
    width: 120px;
    height: 120px;
  }

  .user-info h1 {
    font-size: 1.5rem;
  }
}
