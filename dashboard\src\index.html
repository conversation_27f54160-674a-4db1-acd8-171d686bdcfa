<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Dashboard - User Management</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 32 32%22><defs><linearGradient id=%22grad%22 x1=%220%25%22 y1=%220%25%22 x2=%22100%25%22 y2=%22100%25%22><stop offset=%220%25%22 stop-color=%22%236366f1%22/><stop offset=%22100%25%22 stop-color=%22%238b5cf6%22/></linearGradient></defs><rect width=%2232%22 height=%2232%22 rx=%228%22 fill=%22url(%23grad)%22/><rect x=%226%22 y=%2210%22 width=%2220%22 height=%223%22 rx=%221.5%22 fill=%22white%22/><rect x=%226%22 y=%2215%22 width=%2215%22 height=%222%22 rx=%221%22 fill=%22white%22 opacity=%220.8%22/><rect x=%226%22 y=%2219%22 width=%2212%22 height=%222%22 rx=%221%22 fill=%22white%22 opacity=%220.6%22/></svg>">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body class="mat-typography">
  <app-root></app-root>
</body>
</html>
